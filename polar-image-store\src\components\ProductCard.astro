---
import type { LocalProduct } from '../types/polar';
import { formatPrice } from '../utils/polar';
import { Image } from 'astro:assets';

export interface Props {
  product: LocalProduct;
}

const { product } = Astro.props;
---

<div class="group bg-white rounded-3xl overflow-hidden shadow-sm border border-primary-100 transition-all duration-500 hover:-translate-y-3 hover:shadow-2xl hover:shadow-primary-500/10">
  {product.images.length > 0 && (
    <div class="relative aspect-[4/3] overflow-hidden bg-gradient-to-br from-primary-50 to-accent-50">
      <Image
        src={product.images[0]}
        alt={product.name}
        width={800}
        height={600}
        loading="eager"
        fetchpriority="high"
        class="w-full h-full object-cover transition-all duration-500 group-hover:scale-110"
      />

      <!-- Gradient overlay -->
      <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

      <!-- Hover actions -->
      <div class="absolute inset-0 flex items-center justify-center gap-3 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-4 group-hover:translate-y-0">
        <a
          href={`/products/${product.slug}`}
          class="flex items-center gap-2 px-6 py-3 bg-white/95 backdrop-blur-sm text-primary-900 rounded-full font-semibold text-sm transition-all duration-200 hover:bg-white hover:scale-105 hover:shadow-lg"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
          </svg>
          Preview
        </a>
        <a
          href={`/api/checkout?product_id=${product.id}`}
          class="flex items-center gap-2 px-6 py-3 bg-accent-600 text-white rounded-full font-semibold text-sm transition-all duration-200 hover:bg-accent-700 hover:scale-105 hover:shadow-lg"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v4a2 2 0 01-2 2H9a2 2 0 01-2-2v-4m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
          </svg>
          Buy Now
        </a>
      </div>

      <!-- Price badge -->
      <div class="absolute top-4 right-4 px-3 py-1.5 bg-white/95 backdrop-blur-sm rounded-full">
        <span class="text-lg font-bold text-primary-900">{formatPrice(product.price, product.currency)}</span>
      </div>
    </div>
  )}

  <div class="p-6">
    <!-- Category badge -->
    {product.tags && product.tags.length > 0 && (
      <div class="mb-3">
        <span class="inline-flex items-center px-3 py-1 bg-accent-100 text-accent-700 text-xs font-medium rounded-full">
          {product.tags[0]}
        </span>
      </div>
    )}

    <!-- Title -->
    <h3 class="text-xl font-bold text-primary-900 mb-2 line-clamp-2 group-hover:text-accent-600 transition-colors">
      {product.name}
    </h3>

    <!-- Description -->
    <p class="text-primary-600 text-sm mb-4 line-clamp-2 leading-relaxed">
      {product.description}
    </p>

    <!-- Footer -->
    <div class="flex items-center justify-between pt-4 border-t border-primary-100">
      <div class="flex items-center gap-2 text-xs text-primary-500">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
        </svg>
        <span>Digital Download</span>
      </div>

      <a
        href={`/products/${product.slug}`}
        class="text-accent-600 hover:text-accent-700 font-medium text-sm transition-colors"
      >
        View Details →
      </a>
    </div>
  </div>
</div>
