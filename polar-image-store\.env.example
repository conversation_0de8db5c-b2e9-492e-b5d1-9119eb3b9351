# Polar.sh Configuration (PRODUCTION)
# Get these from https://polar.sh/dashboard/settings/api
POLAR_ACCESS_TOKEN=polar_pat_your_access_token_here
POLAR_WEBHOOK_SECRET=whsec_your_webhook_secret_here
P<PERSON>AR_ORGANIZATION_ID=org_your_organization_id_here

# Environment
NODE_ENV=development
PUBLIC_SITE_URL=http://localhost:4321

# Cloudflare (for production deployment)
# CLOUDFLARE_ACCOUNT_ID=your_cloudflare_account_id
# CLOUDFLARE_API_TOKEN=your_cloudflare_api_token

# Instructions:
# 1. Go to https://polar.sh/dashboard
# 2. Create an organization if you haven't already
# 3. Go to Settings > API to get your access token
# 4. Copy your organization ID from the URL or organization settings
# 5. Create a webhook endpoint and get the secret
# 6. Replace the placeholder values above with your real credentials
