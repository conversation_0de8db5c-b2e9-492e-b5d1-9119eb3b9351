import type { APIRoute } from 'astro';
import { createPolarClient, transformPolarProduct } from '../../utils/polar';

export const prerender = false;

export const GET: APIRoute = async () => {
  try {
    const polar = createPolarClient();
    const organizationId = import.meta.env.POLAR_ORGANIZATION_ID;
    
    if (!organizationId) {
      return new Response(
        JSON.stringify({ error: 'Organization ID not configured' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Get products from Polar
    const response = await polar.products.list({
      organizationId,
      isArchived: false
    });

    // Transform products to local format
    const productList = response.result?.items || [];
    const products = productList.map(transformPolarProduct);

    return new Response(
      JSON.stringify({ products }),
      { 
        status: 200, 
        headers: { 
          'Content-Type': 'application/json',
          'Cache-Control': 'public, max-age=300' // Cache for 5 minutes
        } 
      }
    );
  } catch (error) {
    console.error('Error fetching products:', error);
    return new Response(
      JSON.stringify({ error: 'Failed to fetch products' }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
};
